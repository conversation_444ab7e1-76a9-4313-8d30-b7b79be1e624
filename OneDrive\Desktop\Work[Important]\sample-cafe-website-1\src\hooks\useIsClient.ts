'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to safely determine if code is running on the client side.
 * Prevents hydration mismatches by returning false during SSR and initial hydration.
 * 
 * @returns boolean - true if running on client after hydration, false during SSR
 */
export function useIsClient(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to safely access browser APIs that are only available on the client.
 * Returns undefined during SSR and initial hydration.
 * 
 * @returns object with browser APIs or undefined during SSR
 */
export function useBrowserAPIs() {
  const isClient = useIsClient();

  if (!isClient) {
    return {
      window: undefined,
      document: undefined,
      localStorage: undefined,
      sessionStorage: undefined,
      navigator: undefined,
    };
  }

  return {
    window: typeof window !== 'undefined' ? window : undefined,
    document: typeof document !== 'undefined' ? document : undefined,
    localStorage: typeof localStorage !== 'undefined' ? localStorage : undefined,
    sessionStorage: typeof sessionStorage !== 'undefined' ? sessionStorage : undefined,
    navigator: typeof navigator !== 'undefined' ? navigator : undefined,
  };
}

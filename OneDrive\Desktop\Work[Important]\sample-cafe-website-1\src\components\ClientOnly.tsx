'use client';

import { useEffect, useState } from 'react';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * ClientOnly component prevents hydration mismatches by only rendering
 * children on the client side after hydration is complete.
 * 
 * Use this component to wrap any content that:
 * - Uses browser-only APIs (window, document, localStorage, etc.)
 * - Has different server vs client rendering
 * - Contains random values or timestamps
 * 
 * @param children - Content to render only on client
 * @param fallback - Optional content to show during SSR/hydration
 */
export default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

'use client';

import { useEffect, useState } from 'react';
import { useIsClient, useBrowserAPIs } from '../hooks/useIsClient';
import { formatDateSafe, generateStableId } from '../utils/hydration';
import ClientOnly from './ClientOnly';

/**
 * Example component demonstrating hydration-safe patterns
 */
export default function HydrationSafeExample() {
  const isClient = useIsClient();
  const { window: windowObj, localStorage } = useBrowserAPIs();
  const [currentTime, setCurrentTime] = useState<string>('');
  const [windowSize, setWindowSize] = useState<{ width: number; height: number } | null>(null);

  // Safe way to handle time that changes between server and client
  useEffect(() => {
    setCurrentTime(new Date().toLocaleTimeString());
    
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Safe way to handle window resize
  useEffect(() => {
    if (!windowObj) return;

    const updateWindowSize = () => {
      setWindowSize({
        width: windowObj.innerWidth,
        height: windowObj.innerHeight,
      });
    };

    updateWindowSize();
    windowObj.addEventListener('resize', updateWindowSize);

    return () => windowObj.removeEventListener('resize', updateWindowSize);
  }, [windowObj]);

  // Generate stable IDs that won't change between server and client
  const stableId = generateStableId('example');

  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-xl font-bold mb-4">Hydration Safe Examples</h2>
      
      {/* Safe static content - renders the same on server and client */}
      <div className="mb-4">
        <h3 className="font-semibold">Static Content (Always Safe)</h3>
        <p>This content is the same on server and client.</p>
        <p>Stable ID: {stableId}</p>
        <p>Today's date: {formatDateSafe(new Date())}</p>
      </div>

      {/* Client-only content wrapped in ClientOnly component */}
      <div className="mb-4">
        <h3 className="font-semibold">Client-Only Content</h3>
        <ClientOnly fallback={<p>Loading client content...</p>}>
          <p>Current time: {currentTime}</p>
          {windowSize && (
            <p>Window size: {windowSize.width} x {windowSize.height}</p>
          )}
          <p>User agent: {navigator.userAgent}</p>
        </ClientOnly>
      </div>

      {/* Conditional rendering based on client state */}
      <div className="mb-4">
        <h3 className="font-semibold">Conditional Client Content</h3>
        {isClient ? (
          <div>
            <p>✅ Running on client</p>
            <p>Local storage available: {localStorage ? 'Yes' : 'No'}</p>
          </div>
        ) : (
          <p>🔄 Server-side rendering</p>
        )}
      </div>

      {/* Example of localStorage usage */}
      <div className="mb-4">
        <h3 className="font-semibold">Local Storage Example</h3>
        <ClientOnly fallback={<p>Loading storage info...</p>}>
          <LocalStorageExample />
        </ClientOnly>
      </div>
    </div>
  );
}

/**
 * Separate component for localStorage functionality
 */
function LocalStorageExample() {
  const [storedValue, setStoredValue] = useState<string>('');
  const [inputValue, setInputValue] = useState<string>('');

  useEffect(() => {
    const stored = localStorage.getItem('hydration-example');
    if (stored) {
      setStoredValue(stored);
      setInputValue(stored);
    }
  }, []);

  const handleSave = () => {
    localStorage.setItem('hydration-example', inputValue);
    setStoredValue(inputValue);
  };

  return (
    <div className="space-y-2">
      <p>Stored value: {storedValue || 'None'}</p>
      <div className="flex gap-2">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Enter a value"
          className="border px-2 py-1 rounded"
        />
        <button
          onClick={handleSave}
          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
        >
          Save
        </button>
      </div>
    </div>
  );
}

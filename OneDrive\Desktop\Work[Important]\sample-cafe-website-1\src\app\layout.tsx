import type { Metadata } from "next";
import React, { useRef, useEffect, useState } from "react";
import "./globals.css";
import '../components/GooeyNav'
import GooeyNav from "../components/GooeyNav";

export const metadata: Metadata = {
  title: "Sample Cafe Website",
  description: "Created by prat<PERSON><PERSON> pushkar",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <GooeyNav/>
      <body suppressHydrationWarning>
        {children}
      </body>
    </html>
  );
}

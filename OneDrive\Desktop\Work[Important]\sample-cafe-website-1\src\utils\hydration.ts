/**
 * Utility functions to prevent hydration mismatches in Next.js applications
 */

/**
 * Safely generates a stable ID that won't cause hydration mismatches.
 * Uses a counter-based approach instead of random values.
 */
let idCounter = 0;
export function generateStableId(prefix: string = 'id'): string {
  return `${prefix}-${++idCounter}`;
}

/**
 * Safely formats dates to prevent hydration mismatches.
 * Always returns the same format regardless of timezone differences between server and client.
 */
export function formatDateSafe(date: Date): string {
  return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
}

/**
 * Safely gets current timestamp that won't cause hydration issues.
 * Should only be used in useEffect or event handlers, not in render.
 */
export function getCurrentTimestampSafe(): number | null {
  if (typeof window === 'undefined') {
    return null; // Return null during SSR
  }
  return Date.now();
}

/**
 * Safely checks if we're in a browser environment
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Safely gets window dimensions without causing hydration issues
 */
export function getWindowDimensions(): { width: number; height: number } | null {
  if (!isBrowser()) {
    return null;
  }
  
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  };
}

/**
 * Safely accesses localStorage without causing hydration issues
 */
export function getLocalStorageItem(key: string): string | null {
  if (!isBrowser()) {
    return null;
  }
  
  try {
    return localStorage.getItem(key);
  } catch (error) {
    console.warn('Failed to access localStorage:', error);
    return null;
  }
}

/**
 * Safely sets localStorage without causing hydration issues
 */
export function setLocalStorageItem(key: string, value: string): boolean {
  if (!isBrowser()) {
    return false;
  }
  
  try {
    localStorage.setItem(key, value);
    return true;
  } catch (error) {
    console.warn('Failed to set localStorage:', error);
    return false;
  }
}
